<?php session_start();

/** @var PDO $conexion */
global $conexion;

use App\classes\Hit;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Initialize variables for filters
$filtro_descripcion = '';
$filtro_requester = '';
$filtro_fecha_desde = '';
$filtro_fecha_hasta = '';

// Initialize hits array (empty by default)
$hits = [];

// Handle filter form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_filtrar'])) {
    try {
        // Get filter values
        $filtro_descripcion = limpiar_datos($_POST['filtro_descripcion'] ?? '');
        $filtro_requester = limpiar_datos($_POST['filtro_requester'] ?? '');
        $filtro_fecha_desde = limpiar_datos($_POST['filtro_fecha_desde'] ?? '');
        $filtro_fecha_hasta = limpiar_datos($_POST['filtro_fecha_hasta'] ?? '');

        // Set Bogotá timezone for consistent date handling
        setTimeZoneCol();

        // Get filtered hits using the new method
        $hits = Hit::getHistoricalList(
            $conexion,
            $filtro_descripcion,
            $filtro_requester,
            $filtro_fecha_desde,
            $filtro_fecha_hasta
        );

        $success_display = 'show';
        $success_text = 'Filtros aplicados. Se encontraron ' . count($hits) . ' registros.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}

// Handle clear filters
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_limpiar'])) {
    $filtro_descripcion = '';
    $filtro_requester = '';
    $filtro_fecha_desde = '';
    $filtro_fecha_hasta = '';
    $hits = [];
    
    $success_display = 'show';
    $success_text = 'Filtros limpiados.';
}

require_once __ROOT__ . '/views/lhits_historico.view.php';

?>
